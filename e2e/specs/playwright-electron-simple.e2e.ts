import { getSharedTestContext, resetSharedTestContext, cleanupTestState, type SharedTestContext } from '../helpers/shared-context';
import { expect, test, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Simple Playwright Electron Test
 *
 * This test verifies that our shared Obsidian approach works
 * and demonstrates the basic Playwright Electron functionality.
 */

describe("Playwright Electron - Simple Test", () => {
  let context: SharedTestContext;

  beforeAll(async () => {
    context = await getSharedTestContext();
  });

  beforeEach(async () => {
    await resetSharedTestContext();

    // Wait for Obsidian to be fully loaded
    await context.page.waitForFunction(() => {
      return typeof (window as any).app !== 'undefined' &&
             (window as any).app.workspace !== undefined;
    }, { timeout: 15000 });

    console.log("✅ Obsidian app object is ready");
  });

  afterEach(async () => {
    await cleanupTestState();
  });

  test("should successfully launch Obsidian and access basic functionality", async () => {
    // Verify we can access the Obsidian app object
    const appInfo = await context.page.evaluate(() => {
      const app = (window as any).app;
      return {
        hasApp: typeof app !== 'undefined',
        hasWorkspace: typeof app?.workspace !== 'undefined',
        hasVault: typeof app?.vault !== 'undefined',
        hasCommands: typeof app?.commands !== 'undefined',
        vaultName: app?.vault?.getName?.(),
        commandCount: Object.keys(app?.commands?.commands || {}).length,
      };
    });

    console.log("📊 App info:", appInfo);

    expect(appInfo.hasApp).toBe(true);
    expect(appInfo.hasWorkspace).toBe(true);
    expect(appInfo.hasVault).toBe(true);
    expect(appInfo.hasCommands).toBe(true);
    expect(appInfo.vaultName).toBe('Test');
    expect(appInfo.commandCount).toBeGreaterThan(0);

    console.log("✅ Basic Obsidian functionality verified");
  });

  test("should be able to interact with the UI", async () => {
    // Try to open the command palette
    await context.page.keyboard.press('Meta+P'); // Cmd+P on Mac

    // Wait for command palette to appear
    await context.page.waitForSelector('.prompt-input', { timeout: 5000 });

    console.log("✅ Command palette opened successfully");

    // Type a command
    await context.page.fill('.prompt-input', 'help');

    // Wait for suggestions to appear
    await context.page.waitForSelector('.suggestion-item', { timeout: 2000 });

    const suggestionCount = await context.page.locator('.suggestion-item').count();
    console.log(`📋 Found ${suggestionCount} command suggestions`);

    expect(suggestionCount).toBeGreaterThan(0);

    // Close the command palette
    await context.page.keyboard.press('Escape');

    console.log("✅ UI interaction test completed");
  });

  test("should demonstrate HAR recording capability", async () => {
    // This test doesn't make network requests, but demonstrates that
    // HAR recording would work if we enabled it in the launch options

    const hasNetworkCapability = await context.page.evaluate(() => {
      return typeof fetch !== 'undefined' && typeof XMLHttpRequest !== 'undefined';
    });

    expect(hasNetworkCapability).toBe(true);

    console.log("✅ Network capabilities available for HAR recording");
    console.log("💡 To enable HAR recording, add recordHar option to electron.launch()");
  });
});
