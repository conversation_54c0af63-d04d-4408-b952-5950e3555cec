import { expect, test, beforeAll, afterAll, beforeEach, afterEach, describe, it } from 'vitest';
import { getSharedTestContext, resetSharedTestContext, cleanupTestState, type SharedTestContext } from '../helpers/shared-context';
import { waitForOperation, createTestFile, openFile } from '../helpers/electron-setup';
import { resetObsidianUI, getSyncMetadata } from '../helpers/plugin-setup';

/**
 * Wait for a file to exist in Obsidian's vault
 */
async function waitForFileToExist(page: any, filePath: string, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(
      ({ path }: { path: string }) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        return !!file;
      },
      { path: filePath },
      { timeout }
    );
    return true;
  } catch (error) {
    console.log(`File ${filePath} did not appear within ${timeout}ms`);
    return false;
  }
}

/**
 * Helper to access plugin sync metadata via Obsidian
 * Enhanced with better error handling and file resolution
 */
async function getLocalSyncMetadata(page: any, filePath: string): Promise<any> {
  // First, wait a bit to ensure file operations have completed
  await waitForOperation(500);

  return await page.evaluate(({ path }: { path: string }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin || !plugin.syncMetadata) {
      throw new Error('❌ Ghost sync plugin or syncMetadata not found');
    }

    // Get the TFile object - try multiple path variations
    let file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file && !path.startsWith('/')) {
      // Try with leading slash
      file = (window as any).app.vault.getAbstractFileByPath('/' + path);
    }
    if (!file && path.startsWith('/')) {
      // Try without leading slash
      file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
    }

    if (!file) {
      // List available files for debugging with more detail
      const allFiles = (window as any).app.vault.getAllLoadedFiles()
        .filter((f: any) => f.path.endsWith('.md'))
        .map((f: any) => f.path);

      console.log(`🔍 File not found: ${path}`);
      console.log(`🔍 Available files: ${allFiles.join(', ')}`);
      console.log(`🔍 Total files in vault: ${allFiles.length}`);

      throw new Error(`❌ File not found: ${path}. Available files: ${allFiles.join(', ')}`);
    }

    console.log(`✅ Found file: ${file.path}`);
    const metadata = plugin.syncMetadata.getMetadata(file);
    return metadata || {}; // Return empty object if no metadata exists yet
  }, { path: filePath });
}

/**
 * Helper to get changed_at timestamp for a file
 */
async function getChangedAt(page: any, filePath: string): Promise<string | null> {
  try {
    const metadata = await getLocalSyncMetadata(page, filePath);
    return metadata?.changed_at || null;
  } catch (error: any) {
    // If file doesn't exist or has no metadata yet, return null
    console.log(`No metadata found for ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 * Enhanced with better error handling and verification
 */
async function createLocalTestFile(page: any, filePath: string, content: string): Promise<void> {
  console.log(`📝 Creating test file: ${filePath}`);

  // Retry file creation up to 5 times with exponential backoff
  for (let attempt = 1; attempt <= 5; attempt++) {
    const result = await page.evaluate(async ({ path, fileContent, attemptNum }: { path: string; fileContent: string; attemptNum: number }) => {
      try {
        // Ensure the articles directory exists
        const articlesDir = 'articles';
        const articlesFolder = (window as any).app.vault.getAbstractFileByPath(articlesDir);
        if (!articlesFolder) {
          try {
            await (window as any).app.vault.createFolder(articlesDir);
            console.log(`📁 Created articles directory`);
          } catch (e) {
            // Folder might already exist, ignore error
            console.log(`📁 Articles directory already exists`);
          }
        }

        // Check if file already exists and delete it first
        const existingFile = (window as any).app.vault.getAbstractFileByPath(path);
        if (existingFile) {
          await (window as any).app.vault.delete(existingFile);
          console.log(`🗑️  Deleted existing file: ${path}`);
          // Wait a bit for deletion to complete
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Create the file using Obsidian's vault API
        await (window as any).app.vault.create(path, fileContent);
        return { success: true, message: `✅ Created file: ${path} (attempt ${attemptNum})` };
      } catch (error) {
        return { success: false, error: error.message, attempt: attemptNum };
      }
    }, { path: filePath, fileContent: content, attemptNum: attempt });

    if (result.success) {
      console.log(result.message);

      // Wait for the file to be available in the vault with longer timeout
      const fileExists = await waitForFileToExist(page, filePath, 10000);
      if (fileExists) {
        console.log(`✅ File verified to exist: ${filePath}`);
        return; // Success!
      }
    }

    console.log(`❌ File creation attempt ${attempt} failed: ${result.error || 'File not found after creation'}`);

    if (attempt < 5) {
      // Exponential backoff: 500ms, 1s, 2s, 4s
      const waitTime = 500 * Math.pow(2, attempt - 1);
      await waitForOperation(waitTime);
    }
  }

  // If all attempts failed, list available files for debugging
  const availableFiles = await page.evaluate(() => {
    const allFiles = (window as any).app.vault.getAllLoadedFiles()
      .filter((f: any) => f.path.endsWith('.md'))
      .map((f: any) => f.path);
    return allFiles;
  });

  throw new Error(`❌ Failed to create test file ${filePath} after 5 attempts. Available files: ${availableFiles.join(', ')}`);
}

describe("Ghost Sync - changed_at Handling E2E Tests", () => {
  let context: SharedTestContext;

  beforeAll(async () => {
    context = await getSharedTestContext();
  });

  // afterAll cleanup handled by global teardown

  beforeEach(async () => {
    await resetSharedTestContext();
    await waitForOperation(500);
    await waitForOperation(500);
  });

  afterEach(async () => {
    await resetSharedTestContext();
  });

  test("should set changed_at when manually marking file as changed", async () => {
    const testTitle = "Test Changed At Post";
    const testSlug = "test-changed-at-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test file with frontmatter including a slug
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is test content for changed_at handling.`;

    await createLocalTestFile(context.page, relativeFilePath, content);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForOperation(500);

    // Verify no initial changed_at (should be null for new files)
    const initialChangedAt = await getChangedAt(context.page, relativeFilePath);
    console.log(`Initial changed_at: ${initialChangedAt}`);
    expect(initialChangedAt).toBeNull();

    // Manually mark the file as changed using the plugin's sync metadata
    const markResult = await context.page.evaluate(async (path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin || !plugin.syncMetadata) {
        return { success: false, error: 'Plugin or syncMetadata not found' };
      }

      // Get the TFile object - try both with and without leading slash
      let file = (window as any).app.vault.getAbstractFileByPath(path);
      if (!file && !path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath('/' + path);
      }
      if (!file && path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
      }

      if (!file) {
        const allFiles = (window as any).app.vault.getAllLoadedFiles()
          .filter((f: any) => f.path.endsWith('.md'))
          .map((f: any) => f.path);
        return { success: false, error: `File not found: ${path}. Available: ${allFiles.join(', ')}` };
      }

      try {
        await plugin.syncMetadata.markAsChanged(file);
        return { success: true, filePath: file.path };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }, relativeFilePath);

    console.log('Mark as changed result:', JSON.stringify(markResult, null, 2));
    if (!markResult.success) {
      console.error('❌ markAsChanged failed:', markResult.error || 'Unknown error');
      console.error('Full markResult:', markResult);
    }
    expect(markResult.success).toBe(true);

    await waitForOperation(1000);

    // Check that changed_at was set
    const changedAt = await getChangedAt(context.page, relativeFilePath);

    expect(changedAt).toBeTruthy();
    expect(new Date(changedAt).getTime()).toBeGreaterThan(0);

    // If there was an initial timestamp, verify the new one is different
    if (initialChangedAt) {
      expect(changedAt).not.toBe(initialChangedAt);
    }

    console.log(`✅ changed_at set for file: ${changedAt}`);
  });

  test("should update changed_at timestamp when marked multiple times", async () => {
    const testSlug = "test-multiple-changes";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Multiple Changes"
Slug: "${testSlug}"
Status: "draft"
---

Initial content.`;

    await createLocalTestFile(context.page, relativeFilePath, initialContent);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForOperation(500);

    // Mark as changed first time
    await context.page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForOperation(500);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(context.page, relativeFilePath);
    expect(initialChangedAt).toBeTruthy();

    // Wait a bit to ensure timestamp difference
    await waitForOperation(1000);

    // Mark as changed second time
    await context.page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForOperation(500);

    // Check that changed_at was updated
    const updatedChangedAt = await getChangedAt(context.page, relativeFilePath);

    expect(updatedChangedAt).toBeTruthy();
    expect(updatedChangedAt).not.toBe(initialChangedAt);
    expect(new Date(updatedChangedAt).getTime()).toBeGreaterThan(new Date(initialChangedAt).getTime());

    console.log(`✅ changed_at updated: ${initialChangedAt} -> ${updatedChangedAt}`);
  });

  test("should persist changed_at in sync metadata storage", async () => {
    const testSlug = "test-persistence";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const content = `---
Title: "Test Persistence"
Slug: "${testSlug}"
Status: "draft"
---

Content for persistence test.`;

    await createLocalTestFile(context.page, relativeFilePath, content);

    // Mark as changed
    await context.page.evaluate(({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, { path: relativeFilePath });

    await waitForOperation(500);

    // Get changed_at
    const changedAt = await getChangedAt(context.page, relativeFilePath);
    expect(changedAt).toBeTruthy();

    // Verify it's stored in plugin data (not frontmatter)
    const pluginData = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.loadData();
    });

    const syncMetadata = (await pluginData)?.['sync-metadata'];
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata[relativeFilePath]).toBeTruthy();
    expect(syncMetadata[relativeFilePath].changed_at).toBe(changedAt);

    console.log(`✅ changed_at persisted in sync metadata: ${changedAt}`);
  });
});
