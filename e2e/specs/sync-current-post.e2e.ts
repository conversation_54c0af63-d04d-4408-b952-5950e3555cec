import { expect, test, beforeAll, afterAll, beforeEach, afterEach, describe, it } from 'vitest';
import { getSharedTestContext, resetSharedTestContext, cleanupTestState, type SharedTestContext } from '../helpers/shared-context';
import { waitForOperation, createTestFile, openFile } from '../helpers/electron-setup';
import { getSyncMetadata } from '../helpers/plugin-setup';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * E2E Tests for Sync Current Post to Ghost
 *
 * These tests verify the main sync functionality that users would use most:
 * 1. Sync current post to Ghost via command palette
 * 2. Sync current post to Ghost via ribbon icon
 * 3. Sync current post to Ghost via direct command
 * 4. Handle posts without slugs
 * 5. Handle new posts vs existing posts
 * 6. Verify sync metadata is updated after successful sync
 */





describe("Ghost Sync - Sync Current Post E2E Tests", () => {
  let context: SharedTestContext;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    context = await getSharedTestContext();
  });

  beforeEach(async () => {
    await resetSharedTestContext();

    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('sync-current-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForOperation(200);
  });

  afterEach(async () => {
    await cleanupTestState();
  });

  test("should sync current post to Ghost via command palette", async () => {
    const testTitle = "Sync Current Test Post";
    const testSlug = "sync-current-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post for syncing current post to Ghost via command palette.

## Test Content

Some content to verify the sync works correctly.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Open command palette and sync current post
    await context.page.keyboard.press('Meta+P');

    // Type the sync command
    await context.page.keyboard.type('Sync current post to Ghost');
    await context.page.keyboard.press('Enter');

    console.log("Executed sync current post command via command palette");

    // Wait for sync operation to complete
    await waitForOperation(3000);

    // Verify sync was successful by checking sync metadata
    let syncResult;
    try {
      syncResult = await getSyncMetadata(context.page, relativeFilePath);
    } catch (error: any) {
      // If we can't get sync metadata, fall back to checking notices
      syncResult = { error: error.message, hasSyncedAt: false };
    }

    console.log(`Sync result: ${JSON.stringify(syncResult)}`);

    if (syncResult.error) {
      console.log(`⚠️  Sync verification failed: ${syncResult.error}`);
      // For now, we'll check for notices instead
      const notices = await context.page.evaluate(() => {
        const noticeElements = document.querySelectorAll('.notice');
        return Array.from(noticeElements).map(el => el.textContent);
      });

      const hasSuccessNotice = notices.some((notice: string) =>
        notice?.toLowerCase().includes('sync') ||
        notice?.toLowerCase().includes('success') ||
        notice?.toLowerCase().includes('updated') ||
        notice?.toLowerCase().includes('published') ||
        notice?.toLowerCase().includes('saved')
      );

      console.log(`Notices found: ${notices.join(', ')}`);
      expect(hasSuccessNotice).toBe(true);
      return;
    }

    expect(syncResult.hasSyncedAt).toBe(true);

    console.log(`✅ Successfully synced current post via command palette`);
    console.log(`Synced at: ${syncResult.syncedAt}`);
  });

  test("should sync current post to Ghost via Ghost tab sync button", async () => {
    const testTitle = "Ghost Tab Sync Test Post";
    const testSlug = "ghost-tab-sync-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

This is a test post for syncing via Ghost tab sync button.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Open the Ghost tab first
    await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (plugin && plugin.activateSyncStatusView) {
        plugin.activateSyncStatusView();
      }
    });

    await waitForOperation(1000);

    // Click the sync button in the Ghost tab
    const syncButton = context.page.locator('.ghost-sync-buttons button:has-text("Sync")');
    await syncButton.click();

    console.log("Clicked sync button in Ghost tab");

    // Wait for sync operation to complete
    await waitForOperation(2000);

    // Verify sync was successful
    let syncResult;
    try {
      syncResult = await getSyncMetadata(context.page, relativeFilePath);
    } catch (error: any) {
      // If we can't get sync metadata, fall back to checking notices
      syncResult = { error: error.message, hasSyncedAt: false };
    }

    console.log(`Ghost tab sync result: ${JSON.stringify(syncResult)}`);

    if (syncResult.error) {
      // Check for notices instead
      const notices = await context.page.evaluate(() => {
        const noticeElements = document.querySelectorAll('.notice');
        return Array.from(noticeElements).map(el => el.textContent);
      });

      const hasSuccessNotice = notices.some((notice: string) =>
        notice?.toLowerCase().includes('sync') ||
        notice?.toLowerCase().includes('success') ||
        notice?.toLowerCase().includes('updated')
      );

      console.log(`Ghost tab sync notices: ${notices.join(', ')}`);
      expect(hasSuccessNotice).toBe(true);
      return;
    }

    expect(syncResult.hasSyncedAt).toBe(true);

    console.log(`✅ Successfully synced current post via Ghost tab sync button`);
  });

  test("should reproduce issue: local content changes not synced to Ghost", async () => {
    const testSlug = "sync-content-test-local-changes";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Step 1: Create initial post with specific content
    const initialContent = `---
Title: "Sync Content Test - Local Changes"
Slug: "${testSlug}"
Status: "draft"
Visibility: "public"
---

# Initial Content

This is the initial content that should be synced to Ghost.

## Initial Section

Some initial text here.`;

    await createTestFile(context.page, relativeFilePath, initialContent);
    await openFile(context.page, relativeFilePath);
    console.log(`Created initial test file: ${relativeFilePath}`);

    // Step 2: Sync to Ghost first (create the post)
    await context.page.keyboard.press('Meta+P');
    await context.page.keyboard.type('Sync current post to Ghost');
    await context.page.keyboard.press('Enter');

    await waitForOperation(3000);
    console.log("Initial sync to Ghost completed");

    // Step 3: Modify the local content significantly
    const modifiedContent = `---
Title: "Sync Content Test - Local Changes"
Slug: "${testSlug}"
Status: "draft"
Visibility: "public"
---

# MODIFIED Content - This Should Sync to Ghost

This content has been SIGNIFICANTLY MODIFIED locally and should be synced to Ghost.

## NEW Section Added Locally

This is completely new content added locally.

### Subsection with Important Changes

- New bullet point 1
- New bullet point 2
- Critical information that must be preserved

The original content has been replaced with this new content.`;

    // Update the file content
    await context.page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      if (file) {
        await app.vault.modify(file, content);
        console.log('Modified file content locally');
      }
    }, { path: relativeFilePath, content: modifiedContent });

    await waitForOperation(1000);

    // Step 4: Mark the file as changed to simulate local modifications
    await context.page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForOperation(500);

    // Step 5: Sync again - this should sync local changes TO Ghost
    await context.page.keyboard.press('Meta+P');
    await context.page.keyboard.type('Sync current post to Ghost');
    await context.page.keyboard.press('Enter');

    await waitForOperation(3000);
    console.log("Second sync (with local changes) completed");

    // Step 6: Verify that local content was preserved (not overridden)
    const finalContent = await context.page.evaluate((path) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, relativeFilePath);

    console.log("Final content after sync:", finalContent);

    // ASSERTION: The local modifications should be preserved
    expect(finalContent).toContain("MODIFIED Content - This Should Sync to Ghost");
    expect(finalContent).toContain("NEW Section Added Locally");
    expect(finalContent).toContain("Critical information that must be preserved");
    expect(finalContent).not.toContain("This is the initial content");

    console.log("✅ Local content changes were preserved during sync");
  });
});
