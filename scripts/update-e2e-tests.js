#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update all e2e test files to use the new shared context approach
 * This converts from individual Electron instances to a shared global instance
 */

const fs = require('fs');
const path = require('path');

const E2E_SPECS_DIR = path.join(__dirname, '../e2e/specs');

// Patterns to find and replace
const replacements = [
  {
    // Update imports
    from: /import.*setupElectronTest.*cleanupElectronTest.*type ElectronTestContext.*from.*electron-setup/g,
    to: `import { getSharedTestContext, resetSharedTestContext, cleanupTestState, type SharedTestContext } from '../helpers/shared-context';
import { waitForOperation, createTestFile, openFile } from '../helpers/electron-setup';`
  },
  {
    // Fix syntax errors from previous script run
    from: /from '\.\.\/helpers\/electron-setup';';/g,
    to: `from '../helpers/electron-setup';`
  },
  {
    // Update context type
    from: /let context: ElectronTestContext;/g,
    to: 'let context: SharedTestContext;'
  },
  {
    // Update beforeAll setup
    from: /beforeAll\(async \(\) => \{\s*context = await setupElectronTest\(\);\s*\}\);/g,
    to: `beforeAll(async () => {
    context = await getSharedTestContext();
  });`
  },
  {
    // Remove afterAll cleanup
    from: /afterAll\(async \(\) => \{\s*await cleanupElectronTest\(context\);\s*\}\);/g,
    to: '// afterAll cleanup handled by global teardown'
  },
  {
    // Update beforeEach
    from: /await resetObsidianUI\(context\.page\);/g,
    to: 'await resetSharedTestContext();'
  },
  {
    // Update afterEach
    from: /afterEach\(async \(\) => \{\s*await resetObsidianUI\(context\.page\);\s*\}\);/g,
    to: `afterEach(async () => {
    await cleanupTestState();
  });`
  }
];

function updateTestFile(filePath) {
  console.log(`Updating ${filePath}...`);

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  for (const replacement of replacements) {
    if (replacement.from.test(content)) {
      content = content.replace(replacement.from, replacement.to);
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated ${filePath}`);
  } else {
    console.log(`⏭️ No changes needed for ${filePath}`);
  }
}

function main() {
  console.log('🔄 Updating e2e test files to use shared context...');

  if (!fs.existsSync(E2E_SPECS_DIR)) {
    console.error(`❌ E2E specs directory not found: ${E2E_SPECS_DIR}`);
    process.exit(1);
  }

  const testFiles = fs.readdirSync(E2E_SPECS_DIR)
    .filter(file => file.endsWith('.e2e.ts'))
    .map(file => path.join(E2E_SPECS_DIR, file));

  console.log(`Found ${testFiles.length} test files to update`);

  for (const testFile of testFiles) {
    try {
      updateTestFile(testFile);
    } catch (error) {
      console.error(`❌ Error updating ${testFile}:`, error.message);
    }
  }

  console.log('✅ All test files updated!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Review the updated test files');
  console.log('2. Run: npm run test:e2e');
  console.log('3. Fix any remaining issues manually');
}

if (require.main === module) {
  main();
}

module.exports = { updateTestFile, replacements };
